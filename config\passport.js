 var passport        = require('passport'),
    LocalStrategy   = require('passport-local').Strategy,
    commonService = require("../services/commonService");
    const userService = require('../services/userService');
    bcrypt = require('bcrypt');

module.exports = function(passport) {
    passport.serializeUser(function(user, done) {
        done(null, user.id);
    });

    passport.deserializeUser(async function(id, done) {
        try {
            const detailUser = await userService.findUserById(id);
            console.log('deserializeUser', detailUser);
            if (!detailUser) {
                return done(new Error("Không tìm thấy thông tin người dùng"));
            }
            if (detailUser.active == 0) {
                return done(new Error("Tài khoản chưa kích hoạt"));
            }
            done(null, detailUser);
        } catch (error) {
            done(error);
        }
    });

    // Passport Local Strategy
    passport.use(new LocalStrategy({
        usernameField: 'email', // Chỉ định trường email thay vì username
        passwordField: 'password',
      }, async (email, password, done) => {
        try {
            const user = await userService.findUserByEmail(email);

            if (user) {
                const isValidPassword = await userService.isValidPassword(password, user.password);
                if (!isValidPassword) {
                    return done(null, false, { message: 'Sai mật khẩu.' });
                }
                if (user.active == 0) {
                    return done(null, false, { message: 'Tài khoản chưa được kích hoạt.' });
                }
                return done(null, user);
            } else {
                return done(null, false, { message: 'Tài khoản không tồn tại.' });
            }
        } catch (err) {
            return done(err, false, { message: 'An error occurred during login.' });
        }
    }));
}
