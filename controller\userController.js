const passport = require('passport');
const userService = require('../services/userService');

module.exports = {
  register: async (req, res) => {
    try {
      const { fullname, email, password, confirm_password } = req.body;

      // Validate input
      if (!fullname || !email || !password || !confirm_password) {
        return res.status(400).json({ message: 'Please fill in all fields.' });
      }

      if (password !== confirm_password) {
        return res.status(400).json({ message: 'Passwords do not match.' });
      }

      // Check if email format is valid
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(email)) {
        return res.render('register', { title: 'Register', layout: 'login_layout', messages: { error: 'Invalid email format.' } });
      }

      // Check if email already exists
      const existingUser = await userService.findUserByEmail(email);

      if (existingUser) {
        return res.render('register', { title: 'Register', layout: 'login_layout', messages: { error: 'Email already exists.' } });
      }

      // Create user in database
      const user = await userService.createUser(fullname, email, password);

      // Redirect to login page
      res.redirect('/login');
    } catch (error) {
      console.error(error);
      res.status(500).json({ message: 'An error occurred during registration.' });
    }
  },
  login: passport.authenticate('local', {
    successRedirect: '/',
    failureRedirect: '/login',
    failureFlash: true,
  }, function(err, user, info) {
    if (err) {
      console.error("Authentication error:", err);
      return res.render('login', { title: 'Login', layout: 'login_layout', messages: { error: 'An error occurred during login.' } });
    }
    if (!user) {
      console.log("Authentication failed:", info.message);
      return res.render('login', { title: 'Login', layout: 'login_layout', messages: { error: info.message } });
    }
    req.logIn(user, (err) => {
      if (err) {
        console.error("Login error:", err);
        return res.render('login', { title: 'Login', layout: 'login_layout', messages: { error: 'An error occurred during login.' } });
      }
      return res.redirect('/');
    });
  }.bind(this)),
  logout: (req, res) => {
    req.logout(function (err) {
      if (err) { return next(err); }
      res.redirect('/user/login');
    });
  },
  getRegister: function (req, res, next) {
    console.log('getRegister');
    try {
      if (req.user) {
        res.redirect('/');
      }else{
        res.render('register', { title: 'Register', layout: 'login_layout', messages: { error: '' }, csrfToken: req.csrfToken() });
      }
    } catch (error) {
      console.error(error);
    }
  },
  getLogin: function (req, res, next) {
    console.log('getLogin', req.user);
    try {
      if (req.user) {
        res.redirect('/');
      }else{
        res.render('login', { title: 'Login', layout: 'login_layout', messages: { error: '' }, csrfToken: req.csrfToken() });
      }
    } catch (error) {
      console.error(error);
    }
  },
  checkEmail: async (req, res) => {
    try {
      const { email } = req.body;

      const existingUser = await userService.findUserByEmail(email);

      res.json({ exists: !!existingUser });
    } catch (error) {
      console.error(error);
      res.status(500).json({ message: 'An error occurred during email check.' });
    }
  }
};
