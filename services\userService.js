const bcrypt = require('bcrypt');
const prisma = require('../prisma/client');

module.exports = {
  createUser: async (fullname, email, password) => {
    const hashedPassword = await bcrypt.hash(password, 10);
    const user = await prisma.user.create({
      data: {
        fullname,
        email,
        password: hashedPassword,
      },
    });
    return user;
  },
  findUserByEmail: async (email) => {
    const user = await prisma.user.findUnique({
      where: {
        email: email,
      },
    });
    return user;
  },
  findUserById: async (id) => {
    const user = await prisma.user.findUnique({
      where: {
        id: id,
      },
    });
    return user;
  },
  isValidPassword: async (password, hashedPassword) => {
    return await bcrypt.compare(password, hashedPassword);
  },
};
