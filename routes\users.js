var express = require('express');
var router = express.Router();
const userController = require('../controller/userController');

/* GET users listing. */
router.get('/', function(req, res, next) {
  console.log('user');
  res.send('respond with a resource');
});

router.get('/register', userController.getRegister);
router.post('/register', userController.register);
router.get('/login', userController.getLogin);
router.post('/login', userController.login);
router.get('/logout', userController.logout);

router.post('/check-email', userController.checkEmail);

module.exports = router;
