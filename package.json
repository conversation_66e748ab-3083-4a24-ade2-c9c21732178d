{"name": "backend-coreui", "version": "0.0.0", "private": true, "scripts": {"start": "node ./bin/www"}, "dependencies": {"@prisma/client": "^6.8.2", "axios": "^1.8.1", "bcrypt": "^6.0.0", "cookie-parser": "~1.4.4", "csurf": "^1.11.0", "debug": "~2.6.9", "dotenv": "^16.4.5", "ejs": "^3.1.10", "express": "~4.16.1", "express-ejs-layouts": "^2.5.1", "express-session": "^1.18.1", "http-errors": "~1.6.3", "md5": "^2.3.0", "memorystore": "^1.6.7", "moment": "^2.30.1", "morgan": "~1.9.1", "mysql2": "^3.14.0", "passport": "^0.7.0", "passport-local": "^1.0.0", "serve-favicon": "^2.5.0"}, "devDependencies": {"prisma": "^6.8.2"}}