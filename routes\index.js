var express = require('express');
var router = express.Router();
var indexController = require('../controller/indexController');

// Middleware to check if user is authenticated
function ensureAuthenticated(req, res, next) {
  if (req.isAuthenticated()) {
    return next();
  }
  res.redirect('/user/login');
}

/* GET home page. */
router.get('/', ensureAuthenticated, indexController.index);

module.exports = router;
