/*
  Warnings:

  - You are about to drop the column `role_id` on the `role` table. All the data in the column will be lost.
  - A unique constraint covering the columns `[role_id,user_id]` on the table `Role_User` will be added. If there are existing duplicate values, this will fail.

*/
-- DropIndex
DROP INDEX `Role_role_id_key` ON `role`;

-- AlterTable
ALTER TABLE `role` DROP COLUMN `role_id`;

-- CreateIndex
CREATE UNIQUE INDEX `Role_User_role_id_user_id_key` ON `Role_User`(`role_id`, `user_id`);

-- AddForeignKey
ALTER TABLE `Role_User` ADD CONSTRAINT `Role_User_role_id_fkey` FOREIGN KEY (`role_id`) REFERENCES `Role`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `Role_User` ADD CONSTRAINT `Role_User_user_id_fkey` FOREIGN KEY (`user_id`) REFERENCES `User`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;
