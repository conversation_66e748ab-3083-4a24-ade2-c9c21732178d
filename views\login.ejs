<div class="container">
  <div class="row justify-content-center">
    <div class="col-lg-8">
      <div class="card-group d-block d-md-flex row">
        <div class="card col-md-7 p-4 mb-0">
          <form action="/user/login" method="POST" onsubmit="return validateForm()">
            <div class="card-body">
              <h1>Login</h1>
              <p class="text-body-secondary">Sign In to your account</p>
              <div class="input-group mb-3"><span class="input-group-text">
                  <svg class="icon">
                    <use xlink:href="/icons/sprites/free.svg#cil-user"></use>
                  </svg></span>
                <input class="form-control" type="text" placeholder="Username" name="email" id="email">
              </div>
              <div class="input-group mb-4"><span class="input-group-text">
                  <svg class="icon">
                    <use xlink:href="/icons/sprites/free.svg#cil-lock-locked"></use>
                  </svg></span>
                <input class="form-control" type="password" placeholder="Password" name="password">
              </div>
<input type="hidden" name="_csrf" value="<%= csrfToken %>">
              <div class="row">
                <div class="col-6">
                  <button class="btn btn-primary px-4" type="submit" id="loginBtn">Login</button>
                </div>
                <div class="col-6 text-end">
                  <button class="btn btn-link px-0" type="button">Forgot password?</button>
                </div>
              </div>
              <div id="emailError" style="color: red;"></div>
              <% if (messages && messages.error) { %>
                <div class="alert alert-danger" role="alert">
                  <%= messages.error %>
                </div>
              <% } %>
            </div>
          </form>
        </div>
        <div class="card col-md-5 text-white bg-primary py-5">
          <div class="card-body text-center">
            <div>
              <h2>Sign up</h2>
              <p>Lorem ipsum dolor sit amet, consectetur adipisicing elit, sed do eiusmod tempor incididunt ut labore et
                dolore magna aliqua.</p>
              <button class="btn btn-lg btn-outline-light mt-3" type="button"
                onclick="window.location.href='/user/register'">Register Now!</button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<script>
  document.addEventListener('DOMContentLoaded', function () {
    const emailInput = document.getElementById('email');
    const loginBtn = document.getElementById('loginBtn');
    const emailError = document.getElementById('emailError');
  function validateForm(){
      const email = emailInput.value;
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

      if (!emailRegex.test(email)) {
        emailError.textContent = 'Invalid email format.';
        return false;
      } else {
        emailError.textContent = '';
        return true;
      }
    }
  })
  
</script>
